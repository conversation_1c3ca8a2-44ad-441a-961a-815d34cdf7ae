#!/usr/bin/env python3
"""
测试文件：用于验证Docker外部执行功能
"""

import sys
import os
from datetime import datetime

def main():
    """
    主函数，用于测试Docker外部执行功能
    """
    print("=" * 50)
    print("Docker外部执行测试")
    print("=" * 50)
    
    # 显示基本信息
    print(f"当前时间: {datetime.now()}")
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"脚本路径: {os.path.abspath(__file__)}")
    
    # 显示命令行参数
    if len(sys.argv) > 1:
        print(f"命令行参数: {sys.argv[1:]}")
    else:
        print("没有命令行参数")
    
    # 显示环境变量
    print("\n环境变量:")
    for key in ['PATH', 'HOME', 'USER', 'PWD']:
        value = os.environ.get(key, '未设置')
        print(f"  {key}: {value}")
    
    # 测试文件操作
    print("\n文件系统测试:")
    try:
        files = os.listdir('.')
        print(f"当前目录文件数量: {len(files)}")
        print(f"文件列表: {files[:5]}...")  # 只显示前5个文件
    except Exception as e:
        print(f"文件系统访问错误: {e}")
    
    print("\n✅ Docker外部执行测试完成!")
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
