#!/usr/bin/env node

import inquirer from 'inquirer';
import chalk from 'chalk';
import UserManager from './modules/userManager.js';
import ContainerManager from './modules/containerManager.js';
import AIManager from './modules/aiManager.js';
import FileManager from './modules/fileManager.js';
import CodeExecutor from './modules/codeExecutor.js';

class CodeSpaceManager {
  constructor() {
    this.userManager = new UserManager();
    this.containerManager = new ContainerManager();
    this.aiManager = new AIManager();
    this.fileManager = new FileManager(this.containerManager);
    this.codeExecutor = new CodeExecutor(this.containerManager);
    this.currentUser = null;
  }

  async start() {
    console.log(chalk.blue.bold('\n🚀 欢迎使用 CodeSpace Manager'));
    console.log(chalk.gray('基于 OpenVSCode Server 的智能容器管理系统\n'));

    // 测试AI连接
    console.log(chalk.yellow('正在测试AI连接...'));
    const aiTest = await this.aiManager.testConnection();
    if (aiTest.success) {
      console.log(chalk.green('✅ AI连接成功'));
    } else {
      console.log(chalk.red('❌ AI连接失败:', aiTest.error));
    }

    while (true) {
      try {
        if (!this.currentUser) {
          await this.showAuthMenu();
        } else {
          await this.showMainMenu();
        }
      } catch (error) {
        console.error(chalk.red('发生错误:', error.message));
        await this.pressEnterToContinue();
      }
    }
  }

  async showAuthMenu() {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '请选择操作:',
        choices: [
          { name: '🔐 登录', value: 'login' },
          { name: '📝 注册', value: 'register' },
          { name: '❌ 退出', value: 'exit' }
        ]
      }
    ]);

    switch (action) {
      case 'login':
        await this.handleLogin();
        break;
      case 'register':
        await this.handleRegister();
        break;
      case 'exit':
        console.log(chalk.blue('再见！'));
        process.exit(0);
    }
  }

  async handleLogin() {
    const { username, password } = await inquirer.prompt([
      {
        type: 'input',
        name: 'username',
        message: '用户名:',
        validate: input => input.trim() !== '' || '用户名不能为空'
      },
      {
        type: 'password',
        name: 'password',
        message: '密码:',
        validate: input => input.trim() !== '' || '密码不能为空'
      }
    ]);

    try {
      const result = await this.userManager.loginUser(username, password);
      if (result.success) {
        this.currentUser = result.user;
        console.log(chalk.green(`✅ 登录成功！欢迎回来，${username}`));

        // 检查或创建用户容器
        await this.ensureUserContainer();
      }
    } catch (error) {
      console.log(chalk.red('❌ 登录失败:', error.message));
      await this.pressEnterToContinue();
    }
  }

  async handleRegister() {
    const { username, password, confirmPassword } = await inquirer.prompt([
      {
        type: 'input',
        name: 'username',
        message: '用户名:',
        validate: input => input.trim() !== '' || '用户名不能为空'
      },
      {
        type: 'password',
        name: 'password',
        message: '密码:',
        validate: input => input.length >= 6 || '密码至少6位'
      },
      {
        type: 'password',
        name: 'confirmPassword',
        message: '确认密码:',
        validate: (input, answers) => input === answers.password || '密码不匹配'
      }
    ]);

    try {
      const result = await this.userManager.registerUser(username, password);
      if (result.success) {
        console.log(chalk.green(`✅ 注册成功！用户ID: ${result.userId}`));
        console.log(chalk.yellow('请使用新账户登录'));
      }
    } catch (error) {
      console.log(chalk.red('❌ 注册失败:', error.message));
    }

    await this.pressEnterToContinue();
  }

  async ensureUserContainer() {
    console.log(chalk.yellow('正在检查容器状态...'));

    try {
      // 首先检查是否已有容器记录
      const existingContainer = await this.containerManager.getUserContainer(this.currentUser.id);

      if (existingContainer) {
        console.log(chalk.cyan(`📦 发现现有容器: ${existingContainer.containerId.substring(0, 12)}`));

        // 检查容器是否还在运行
        const containerStatus = await this.containerManager.getContainerStatus(this.currentUser.id);

        if (containerStatus.exists && containerStatus.running) {
          console.log(chalk.green(`✅ 容器已在运行`));
          console.log(chalk.blue(`🌐 访问地址: http://localhost:${existingContainer.port}`));
          this.currentUser.containerId = existingContainer.containerId;
          return;
        } else if (containerStatus.exists && !containerStatus.running) {
          console.log(chalk.yellow('🔄 启动现有容器...'));
          const startResult = await this.containerManager.startUserContainer(this.currentUser.id);
          if (startResult.success) {
            console.log(chalk.green(`✅ 容器已启动`));
            console.log(chalk.blue(`🌐 访问地址: http://localhost:${startResult.port}`));
            this.currentUser.containerId = existingContainer.containerId;
            return;
          } else {
            console.log(chalk.yellow('⚠️ 启动失败，将创建新容器'));
          }
        } else {
          console.log(chalk.yellow('⚠️ 容器不存在，将创建新容器'));
        }
      } else {
        console.log(chalk.cyan('🆕 首次登录，创建新容器...'));
      }

      // 创建新容器
      const containerInfo = await this.containerManager.createUserContainer(
        this.currentUser.id,
        this.currentUser.username
      );

      if (containerInfo) {
        console.log(chalk.green(`✅ 容器创建成功: ${containerInfo.containerId.substring(0, 12)}`));
        console.log(chalk.blue(`🌐 访问地址: http://localhost:${containerInfo.port}`));

        // 更新用户的容器ID
        await this.userManager.updateUserContainer(this.currentUser.username, containerInfo.containerId);
        this.currentUser.containerId = containerInfo.containerId;
      }
    } catch (error) {
      console.log(chalk.red('❌ 容器操作失败:', error.message));
    }
  }

  async showMainMenu() {
    console.log(chalk.cyan(`\n👤 当前用户: ${this.currentUser.username}`));
    console.log(chalk.gray(`🆔 用户ID: ${this.currentUser.id}`));

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '请选择操作:',
        choices: [
          { name: '🤖 AI助手', value: 'ai' },
          { name: '📁 文件管理', value: 'files' },
          { name: '⚡ 代码执行', value: 'execute' },
          { name: '🌐 打开VSCode', value: 'vscode' },
          { name: '🔧 系统信息', value: 'system' },
          { name: '🚪 退出登录', value: 'logout' }
        ]
      }
    ]);

    switch (action) {
      case 'ai':
        await this.showAIMenu();
        break;
      case 'files':
        await this.showFileMenu();
        break;
      case 'execute':
        await this.showExecuteMenu();
        break;
      case 'vscode':
        await this.openVSCode();
        break;
      case 'system':
        await this.showSystemInfo();
        break;
      case 'logout':
        this.currentUser = null;
        console.log(chalk.yellow('已退出登录'));
        break;
    }
  }

  async showAIMenu() {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '选择AI功能:',
        choices: [
          { name: '💬 自由对话', value: 'chat' },
          { name: '📝 生成代码', value: 'generate' },
          { name: '🔙 返回主菜单', value: 'back' }
        ]
      }
    ]);

    switch (action) {
      case 'chat':
        await this.handleAIChat();
        break;
      case 'generate':
        await this.handleCodeGeneration();
        break;
      case 'back':
        return;
    }
  }

  async handleAIChat() {
    const { prompt } = await inquirer.prompt([
      {
        type: 'input',
        name: 'prompt',
        message: '请输入您的问题:',
        validate: input => input.trim() !== '' || '问题不能为空'
      }
    ]);

    console.log(chalk.yellow('AI正在思考...'));
    const result = await this.aiManager.generateResponse(prompt);

    if (result.success) {
      console.log(chalk.green('\n🤖 AI回复:'));
      console.log(chalk.white(result.response));
    } else {
      console.log(chalk.red('❌ AI请求失败:', result.error));
    }

    await this.pressEnterToContinue();
  }

  async handleCodeGeneration() {
    const { description, language } = await inquirer.prompt([
      {
        type: 'input',
        name: 'description',
        message: '请描述您想要的代码功能:',
        validate: input => input.trim() !== '' || '描述不能为空'
      },
      {
        type: 'list',
        name: 'language',
        message: '选择编程语言:',
        choices: ['python', 'javascript', 'java', 'cpp', 'bash']
      }
    ]);

    console.log(chalk.yellow('AI正在生成代码...'));
    const result = await this.aiManager.generateCodeFromDescription(description, language);

    if (result.success) {
      console.log(chalk.green('\n💻 生成的代码:'));
      console.log(chalk.white(result.response));

      const { saveCode } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'saveCode',
          message: '是否保存代码到容器中?',
          default: false
        }
      ]);

      if (saveCode) {
        await this.saveGeneratedCode(result.response, language);
      }
    } else {
      console.log(chalk.red('❌ 代码生成失败:', result.error));
    }

    await this.pressEnterToContinue();
  }

  async saveGeneratedCode(code, language) {
    const extensions = {
      python: 'py',
      javascript: 'js',
      java: 'java',
      cpp: 'cpp',
      bash: 'sh'
    };

    const { filename } = await inquirer.prompt([
      {
        type: 'input',
        name: 'filename',
        message: `文件名 (不含扩展名):`,
        default: `generated_${Date.now()}`,
        validate: input => input.trim() !== '' || '文件名不能为空'
      }
    ]);

    const fullFilename = `${filename}.${extensions[language]}`;
    const result = await this.fileManager.writeFile(this.currentUser.id, `/home/<USER>/${fullFilename}`, code);

    if (result.success) {
      console.log(chalk.green(`✅ 代码已保存为: ${fullFilename}`));
    } else {
      console.log(chalk.red('❌ 保存失败:', result.error));
    }
  }

  async showFileMenu() {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '选择文件操作:',
        choices: [
          { name: '📋 列出文件', value: 'list' },
          { name: '👀 查看文件', value: 'read' },
          { name: '✏️ 编辑文件', value: 'write' },
          { name: '📁 创建目录', value: 'mkdir' },
          { name: '🔙 返回主菜单', value: 'back' }
        ]
      }
    ]);

    switch (action) {
      case 'list':
        await this.listFiles();
        break;
      case 'read':
        await this.readFile();
        break;
      case 'write':
        await this.writeFile();
        break;
      case 'mkdir':
        await this.createDirectory();
        break;
      case 'back':
        return;
    }
  }

  async listFiles() {
    const { path } = await inquirer.prompt([
      {
        type: 'input',
        name: 'path',
        message: '目录路径:',
        default: '/home/<USER>'
      }
    ]);

    console.log(chalk.yellow('正在列出文件...'));
    const result = await this.fileManager.listFiles(this.currentUser.id, path);

    if (result.success) {
      console.log(chalk.green(`\n📁 目录: ${result.path}`));
      if (result.files.length === 0) {
        console.log(chalk.gray('目录为空'));
      } else {
        result.files.forEach(file => {
          const icon = file.isDirectory ? '📁' : '📄';
          const color = file.isDirectory ? chalk.blue : chalk.white;
          console.log(color(`${icon} ${file.name} (${file.size})`));
        });
      }
    } else {
      console.log(chalk.red('❌ 列出文件失败:', result.error));
    }

    await this.pressEnterToContinue();
  }

  async readFile() {
    const { filePath } = await inquirer.prompt([
      {
        type: 'input',
        name: 'filePath',
        message: '文件路径:',
        validate: input => input.trim() !== '' || '文件路径不能为空'
      }
    ]);

    console.log(chalk.yellow('正在读取文件...'));
    const result = await this.fileManager.readFile(this.currentUser.id, filePath);

    if (result.success) {
      console.log(chalk.green(`\n📄 文件内容: ${result.path}`));
      console.log(chalk.white('─'.repeat(50)));
      console.log(result.content);
      console.log(chalk.white('─'.repeat(50)));
    } else {
      console.log(chalk.red('❌ 读取文件失败:', result.error));
    }

    await this.pressEnterToContinue();
  }

  async writeFile() {
    const { filePath, content } = await inquirer.prompt([
      {
        type: 'input',
        name: 'filePath',
        message: '文件路径:',
        validate: input => input.trim() !== '' || '文件路径不能为空'
      },
      {
        type: 'editor',
        name: 'content',
        message: '文件内容 (将打开编辑器):'
      }
    ]);

    console.log(chalk.yellow('正在写入文件...'));
    const result = await this.fileManager.writeFile(this.currentUser.id, filePath, content);

    if (result.success) {
      console.log(chalk.green(`✅ 文件写入成功: ${result.path}`));
    } else {
      console.log(chalk.red('❌ 写入文件失败:', result.error));
    }

    await this.pressEnterToContinue();
  }

  async createDirectory() {
    const { dirPath } = await inquirer.prompt([
      {
        type: 'input',
        name: 'dirPath',
        message: '目录路径:',
        validate: input => input.trim() !== '' || '目录路径不能为空'
      }
    ]);

    console.log(chalk.yellow('正在创建目录...'));
    const result = await this.fileManager.createDirectory(this.currentUser.id, `/home/<USER>/${dirPath}`);

    if (result.success) {
      console.log(chalk.green(`✅ 目录创建成功: ${result.path}`));
    } else {
      console.log(chalk.red('❌ 创建目录失败:', result.error));
    }

    await this.pressEnterToContinue();
  }

  async showExecuteMenu() {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '选择执行操作:',
        choices: [
          { name: '⚡ 执行命令', value: 'command' },
          { name: '📄 运行文件', value: 'file' },
          { name: '🐳 Docker环境配置', value: 'docker' },
          { name: '📦 安装包', value: 'install' },
          { name: '🔙 返回主菜单', value: 'back' }
        ]
      }
    ]);

    switch (action) {
      case 'command':
        await this.executeCommand();
        break;
      case 'file':
        await this.runFile();
        break;
      case 'docker':
        await this.showDockerMenu();
        break;
      case 'install':
        await this.installPackage();
        break;
      case 'back':
        return;
    }
  }

  async showDockerMenu() {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '选择Docker操作:',
        choices: [
          { name: '🚀 启动OpenVSCode Server', value: 'openvscode' },
          { name: '🔧 Docker环境配置', value: 'environment' },
          { name: '🎯 Docker外部执行文件', value: 'external' },
          { name: '⚙️ 自定义Docker命令', value: 'custom' },
          { name: '📋 查看Docker状态', value: 'status' },
          { name: '🔙 返回执行菜单', value: 'back' }
        ]
      }
    ]);

    switch (action) {
      case 'openvscode':
        await this.runOpenVSCodeServer();
        break;
      case 'environment':
        await this.showDockerEnvironmentMenu();
        break;
      case 'external':
        await this.runDockerExternalFile();
        break;
      case 'custom':
        await this.runCustomDockerCommand();
        break;
      case 'status':
        await this.showDockerStatus();
        break;
      case 'back':
        return;
    }
  }

  async runOpenVSCodeServer() {
    const { port, volumePath } = await inquirer.prompt([
      {
        type: 'input',
        name: 'port',
        message: '端口号:',
        default: '7030',
        validate: input => {
          const port = parseInt(input);
          return (port > 0 && port < 65536) || '请输入有效的端口号 (1-65535)';
        }
      },
      {
        type: 'input',
        name: 'volumePath',
        message: '本地工作目录路径:',
        default: '/home/<USER>',
        validate: input => input.trim() !== '' || '路径不能为空'
      }
    ]);

    // 构建Docker命令，修复原始命令中的问题
    const dockerCommand = `docker run -it --init -p ${port}:3000 -v "${volumePath}:/home/<USER>" gitpod/openvscode-server`;

    console.log(chalk.blue('🐳 Docker命令:'));
    console.log(chalk.white(dockerCommand));
    console.log(chalk.yellow('\n正在启动OpenVSCode Server...'));
    console.log(chalk.gray('注意: 这将在后台启动容器，请稍等片刻'));

    const result = await this.executeDockerCommand(dockerCommand, 60000);

    if (result.success) {
      console.log(chalk.green(`✅ OpenVSCode Server启动成功!`));
      console.log(chalk.blue(`🌐 访问地址: http://localhost:${port}`));
      console.log(chalk.gray('容器将在后台运行，您可以在浏览器中访问上述地址'));
    } else {
      console.log(chalk.red('❌ 启动失败'));
      console.log(chalk.yellow('💡 建议检查:'));
      console.log(chalk.gray('1. Docker是否已安装并运行'));
      console.log(chalk.gray('2. 端口是否被占用'));
      console.log(chalk.gray('3. 路径是否正确'));
    }

    this.displayExecutionResult(result);
    await this.pressEnterToContinue();
  }

  async runCustomDockerCommand() {
    const { dockerCommand, timeout } = await inquirer.prompt([
      {
        type: 'input',
        name: 'dockerCommand',
        message: 'Docker命令:',
        validate: input => input.trim() !== '' || 'Docker命令不能为空'
      },
      {
        type: 'input',
        name: 'timeout',
        message: '超时时间(秒):',
        default: '60',
        validate: input => {
          const time = parseInt(input);
          return (time > 0) || '请输入有效的超时时间';
        }
      }
    ]);

    console.log(chalk.yellow('正在执行Docker命令...'));
    const result = await this.executeDockerCommand(
      dockerCommand,
      parseInt(timeout) * 1000
    );

    this.displayExecutionResult(result);
    await this.pressEnterToContinue();
  }

  async showDockerStatus() {
    console.log(chalk.yellow('正在检查Docker状态...'));

    const commands = [
      { name: 'Docker版本', command: 'docker --version' },
      { name: 'Docker状态', command: 'docker info --format "{{.ServerVersion}}"' },
      { name: '运行中的容器', command: 'docker ps --format "table {{.Names}}\\t{{.Image}}\\t{{.Status}}\\t{{.Ports}}"' },
      { name: '所有容器', command: 'docker ps -a --format "table {{.Names}}\\t{{.Image}}\\t{{.Status}}"' }
    ];

    for (const cmd of commands) {
      console.log(chalk.cyan(`\n📊 ${cmd.name}:`));
      const result = await this.executeDockerCommand(cmd.command, 10000);

      if (result.success && result.stdout) {
        console.log(chalk.white(result.stdout));
      } else {
        console.log(chalk.red(`❌ 获取失败: ${result.stderr || '未知错误'}`));
      }
    }

    await this.pressEnterToContinue();
  }

  async executeCommand() {
    const { command, workingDir } = await inquirer.prompt([
      {
        type: 'input',
        name: 'command',
        message: '要执行的命令:',
        validate: input => input.trim() !== '' || '命令不能为空'
      },
      {
        type: 'input',
        name: 'workingDir',
        message: '工作目录:',
        default: '/home/<USER>'
      }
    ]);

    console.log(chalk.yellow('正在执行命令...'));
    const result = await this.codeExecutor.executeCommand(this.currentUser.id, command, workingDir);

    this.displayExecutionResult(result);
    await this.pressEnterToContinue();
  }

  displayExecutionResult(result) {
    console.log(chalk.cyan('\n📊 执行结果:'));
    console.log(chalk.gray(`命令: ${result.command}`));
    console.log(chalk.gray(`工作目录: ${result.workingDir}`));
    console.log(chalk.gray(`退出码: ${result.exitCode}`));
    console.log(chalk.gray(`状态: ${result.success ? '✅ 成功' : '❌ 失败'}`));

    if (result.stdout) {
      console.log(chalk.green('\n📤 标准输出:'));
      console.log(result.stdout);
    }

    if (result.stderr) {
      console.log(chalk.red('\n📥 错误输出:'));
      console.log(result.stderr);
    }
  }

  async runFile() {
    // 首先列出当前目录的文件
    console.log(chalk.yellow('正在获取文件列表...'));
    const fileListResult = await this.fileManager.listFiles(this.currentUser.id, '/home/<USER>');

    if (!fileListResult.success) {
      console.log(chalk.red('❌ 获取文件列表失败:', fileListResult.error));
      await this.pressEnterToContinue();
      return;
    }

    const executableFiles = fileListResult.files.filter(file =>
      !file.isDirectory &&
      (file.name.endsWith('.py') ||
       file.name.endsWith('.js') ||
       file.name.endsWith('.java') ||
       file.name.endsWith('.cpp') ||
       file.name.endsWith('.c') ||
       file.name.endsWith('.sh'))
    );

    if (executableFiles.length === 0) {
      console.log(chalk.yellow('📁 当前目录没有可执行的代码文件'));
      console.log(chalk.gray('支持的文件类型: .py, .js, .java, .cpp, .c, .sh'));
      await this.pressEnterToContinue();
      return;
    }

    console.log(chalk.green('\n📁 可执行文件列表:'));
    executableFiles.forEach((file, index) => {
      const icon = this.getFileIcon(file.name);
      console.log(chalk.white(`${index + 1}. ${icon} ${file.name}`));
    });

    const { choice } = await inquirer.prompt([
      {
        type: 'list',
        name: 'choice',
        message: '选择要运行的文件:',
        choices: [
          ...executableFiles.map((file, index) => ({
            name: `${this.getFileIcon(file.name)} ${file.name}`,
            value: file.name
          })),
          { name: '🔙 返回', value: 'back' }
        ]
      }
    ]);

    if (choice === 'back') return;

    const { args } = await inquirer.prompt([
      {
        type: 'input',
        name: 'args',
        message: '命令行参数 (可选):',
        default: ''
      }
    ]);

    console.log(chalk.yellow(`正在运行文件: ${choice}`));
    const result = await this.codeExecutor.runFile(this.currentUser.id, choice, args);

    this.displayExecutionResult(result);
    await this.pressEnterToContinue();
  }

  getFileIcon(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    const icons = {
      'py': '🐍',
      'js': '🟨',
      'java': '☕',
      'cpp': '⚙️',
      'c': '🔧',
      'sh': '📜'
    };
    return icons[ext] || '📄';
  }

  async installPackage() {
    const { packageManager, packageName } = await inquirer.prompt([
      {
        type: 'list',
        name: 'packageManager',
        message: '选择包管理器:',
        choices: [
          { name: '🐍 pip (Python)', value: 'pip' },
          { name: '📦 npm (Node.js)', value: 'npm' },
          { name: '🔧 apt (系统包)', value: 'apt' }
        ]
      },
      {
        type: 'input',
        name: 'packageName',
        message: '包名:',
        validate: input => input.trim() !== '' || '包名不能为空'
      }
    ]);

    console.log(chalk.yellow(`正在安装 ${packageName}...`));
    const result = await this.codeExecutor.installPackage(this.currentUser.id, packageManager, packageName);

    this.displayExecutionResult(result);
    await this.pressEnterToContinue();
  }

  async openVSCode() {
    const containerInfo = await this.containerManager.getUserContainer(this.currentUser.id);
    if (containerInfo) {
      const url = `http://localhost:${containerInfo.port}`;
      console.log(chalk.blue(`🌐 VSCode访问地址: ${url}`));
      console.log(chalk.yellow('请在浏览器中打开上述地址'));
    } else {
      console.log(chalk.red('❌ 容器不存在'));
    }

    await this.pressEnterToContinue();
  }

  async showSystemInfo() {
    console.log(chalk.yellow('正在获取系统信息...'));
    const result = await this.codeExecutor.getSystemInfo(this.currentUser.id);

    if (result.success) {
      console.log(chalk.green('\n🔧 系统信息:'));
      Object.entries(result.systemInfo).forEach(([key, info]) => {
        const status = info.available ? '✅' : '❌';
        console.log(chalk.cyan(`${status} ${key.toUpperCase()}:`));
        console.log(chalk.white(`   ${info.output}`));
      });
    } else {
      console.log(chalk.red('❌ 获取系统信息失败:', result.error));
    }

    await this.pressEnterToContinue();
  }

  async showDockerEnvironmentMenu() {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '选择Docker环境配置:',
        choices: [
          { name: '🔄 更新系统包 (apt update)', value: 'apt_update' },
          { name: '🐍 安装Python3和pip', value: 'install_python' },
          { name: '📦 批量环境配置', value: 'batch_setup' },
          { name: '🔧 自定义环境命令', value: 'custom_env' },
          { name: '🔙 返回Docker菜单', value: 'back' }
        ]
      }
    ]);

    switch (action) {
      case 'apt_update':
        await this.runDockerAptUpdate();
        break;
      case 'install_python':
        await this.runDockerInstallPython();
        break;
      case 'batch_setup':
        await this.runDockerBatchSetup();
        break;
      case 'custom_env':
        await this.runDockerCustomEnvironment();
        break;
      case 'back':
        return;
    }
  }

  async runDockerAptUpdate() {
    console.log(chalk.yellow('正在更新Docker容器中的系统包...'));

    const commands = [
      'docker exec --user root $(docker ps -q --filter "ancestor=gitpod/openvscode-server") apt update'
    ];

    for (const command of commands) {
      console.log(chalk.blue(`执行: ${command}`));
      const result = await this.executeDockerCommand(command);
      this.displayExecutionResult(result);

      if (!result.success) {
        console.log(chalk.red('❌ 命令执行失败，停止后续操作'));
        break;
      }
    }

    await this.pressEnterToContinue();
  }

  async runDockerInstallPython() {
    console.log(chalk.yellow('正在在Docker容器中安装Python3和pip...'));

    const commands = [
      'docker exec --user root $(docker ps -q --filter "ancestor=gitpod/openvscode-server") apt update',
      'docker exec --user root $(docker ps -q --filter "ancestor=gitpod/openvscode-server") apt install -y python3 python3-pip'
    ];

    for (const command of commands) {
      console.log(chalk.blue(`执行: ${command}`));
      const result = await this.executeDockerCommand(command);
      this.displayExecutionResult(result);

      if (!result.success) {
        console.log(chalk.red('❌ 命令执行失败，停止后续操作'));
        break;
      }
    }

    await this.pressEnterToContinue();
  }

  async runDockerBatchSetup() {
    console.log(chalk.yellow('正在执行批量Docker环境配置...'));

    const commands = [
      'docker exec --user root $(docker ps -q --filter "ancestor=gitpod/openvscode-server") apt update',
      'docker exec --user root $(docker ps -q --filter "ancestor=gitpod/openvscode-server") apt install -y python3 python3-pip',
      'sudo apt update && sudo apt install -y python3 python3-pip'
    ];

    for (const command of commands) {
      console.log(chalk.blue(`执行: ${command}`));
      const result = await this.executeDockerCommand(command);
      this.displayExecutionResult(result);

      if (!result.success) {
        console.log(chalk.yellow('⚠️ 命令执行失败，继续执行下一个命令'));
      }
    }

    console.log(chalk.green('✅ 批量环境配置完成'));
    await this.pressEnterToContinue();
  }

  async runDockerCustomEnvironment() {
    const { dockerCommand, timeout } = await inquirer.prompt([
      {
        type: 'input',
        name: 'dockerCommand',
        message: 'Docker环境配置命令:',
        validate: input => input.trim() !== '' || 'Docker命令不能为空'
      },
      {
        type: 'input',
        name: 'timeout',
        message: '超时时间(秒):',
        default: '120',
        validate: input => {
          const time = parseInt(input);
          return (time > 0) || '请输入有效的超时时间';
        }
      }
    ]);

    console.log(chalk.yellow('正在执行自定义Docker环境配置命令...'));
    const result = await this.executeDockerCommand(dockerCommand, parseInt(timeout) * 1000);
    this.displayExecutionResult(result);
    await this.pressEnterToContinue();
  }

  async runDockerExternalFile() {
    const { containerName, filePath, args } = await inquirer.prompt([
      {
        type: 'input',
        name: 'containerName',
        message: 'Docker容器名称:',
        default: 'vscode-server-with-python',
        validate: input => input.trim() !== '' || '容器名称不能为空'
      },
      {
        type: 'input',
        name: 'filePath',
        message: '要执行的文件路径:',
        default: '/home/<USER>/hello_world.py',
        validate: input => input.trim() !== '' || '文件路径不能为空'
      },
      {
        type: 'input',
        name: 'args',
        message: '命令行参数 (可选):',
        default: ''
      }
    ]);

    // 根据文件扩展名确定执行命令
    const extension = filePath.split('.').pop().toLowerCase();
    let executeCommand;

    switch (extension) {
      case 'py':
        executeCommand = `python3 ${filePath} ${args}`.trim();
        break;
      case 'js':
        executeCommand = `node ${filePath} ${args}`.trim();
        break;
      case 'sh':
        executeCommand = `bash ${filePath} ${args}`.trim();
        break;
      case 'java':
        const className = filePath.replace(/.*\//, '').replace('.java', '');
        executeCommand = `cd $(dirname ${filePath}) && javac $(basename ${filePath}) && java ${className} ${args}`.trim();
        break;
      default:
        executeCommand = `${filePath} ${args}`.trim();
    }

    const dockerCommand = `docker exec ${containerName} ${executeCommand}`;

    console.log(chalk.yellow('正在执行Docker外部文件...'));
    console.log(chalk.blue(`Docker命令: ${dockerCommand}`));

    const result = await this.executeDockerCommand(dockerCommand);
    this.displayExecutionResult(result);
    await this.pressEnterToContinue();
  }

  async executeDockerCommand(command, timeout = 60000) {
    try {
      // 检查命令是否包含命令替换，如果有则使用专门的方法处理
      if (command.includes('$(docker ps')) {
        const result = await this.codeExecutor.executeDockerContainerCommand(
          command,
          process.cwd(),
          timeout
        );
        return result;
      } else {
        // 直接在主机上执行docker命令
        const result = await this.codeExecutor.executeHostCommand(
          command,
          process.cwd(),
          timeout
        );
        return result;
      }
    } catch (error) {
      console.error('执行Docker命令失败:', error);
      return {
        success: false,
        exitCode: -1,
        stdout: '',
        stderr: error.message,
        command
      };
    }
  }

  async pressEnterToContinue() {
    await inquirer.prompt([
      {
        type: 'input',
        name: 'continue',
        message: '按回车键继续...'
      }
    ]);
  }
}

// 启动应用
const app = new CodeSpaceManager();
app.start().catch(error => {
  console.error(chalk.red('应用启动失败:', error));
  process.exit(1);
});