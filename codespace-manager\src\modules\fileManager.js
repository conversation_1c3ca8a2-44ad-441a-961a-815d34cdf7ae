import Docker from 'dockerode';
import tar from 'tar-stream';
import fs from 'fs-extra';
import path from 'path';

class FileManager {
  constructor(containerManager) {
    this.docker = new Docker();
    this.containerManager = containerManager;
  }

  async listFiles(userId, containerPath = '/home/<USER>') {
    try {
      const containerInfo = await this.containerManager.getUserContainer(userId);
      if (!containerInfo) {
        throw new Error('用户容器不存在');
      }

      const container = this.docker.getContainer(containerInfo.containerId);

      // 执行 ls 命令列出文件
      const exec = await container.exec({
        Cmd: ['ls', '-la', containerPath],
        AttachStdout: true,
        AttachStderr: true
      });

      const stream = await exec.start();

      return new Promise((resolve, reject) => {
        let output = '';
        stream.on('data', (chunk) => {
          output += chunk.toString();
        });

        stream.on('end', () => {
          const files = this.parseListOutput(output);
          resolve({ success: true, files, path: containerPath });
        });

        stream.on('error', reject);
      });
    } catch (error) {
      console.error('列出文件失败:', error);
      return { success: false, error: error.message };
    }
  }

  parseListOutput(output) {
    const lines = output.split('\n').filter(line => line.trim());
    const files = [];

    for (const line of lines) {
      // 跳过总计行和空行
      if (line.startsWith('total') || !line.trim()) continue;

      const parts = line.split(/\s+/);
      if (parts.length >= 9) {
        const permissions = parts[0];
        const size = parts[4];
        const name = parts.slice(8).join(' ');

        // 跳过 . 和 .. 目录
        if (name === '.' || name === '..') continue;

        files.push({
          name,
          type: permissions.startsWith('d') ? 'directory' : 'file',
          permissions,
          size,
          isDirectory: permissions.startsWith('d')
        });
      }
    }

    return files;
  }

  async readFile(userId, filePath) {
    try {
      const containerInfo = await this.containerManager.getUserContainer(userId);
      if (!containerInfo) {
        throw new Error('用户容器不存在');
      }

      const container = this.docker.getContainer(containerInfo.containerId);

      // 使用 cat 命令读取文件内容
      const exec = await container.exec({
        Cmd: ['cat', filePath],
        AttachStdout: true,
        AttachStderr: true
      });

      const stream = await exec.start();

      return new Promise((resolve, reject) => {
        let content = '';
        let error = '';

        stream.on('data', (chunk) => {
          const data = chunk.toString();
          // Docker exec 输出包含控制字符，需要处理
          if (chunk[0] === 1) { // stdout
            content += data.slice(8);
          } else if (chunk[0] === 2) { // stderr
            error += data.slice(8);
          }
        });

        stream.on('end', () => {
          if (error) {
            reject(new Error(error));
          } else {
            resolve({ success: true, content, path: filePath });
          }
        });

        stream.on('error', reject);
      });
    } catch (error) {
      console.error('读取文件失败:', error);
      return { success: false, error: error.message };
    }
  }

  async writeFile(userId, filePath, content) {
    try {
      const containerInfo = await this.containerManager.getUserContainer(userId);
      if (!containerInfo) {
        throw new Error('用户容器不存在');
      }

      const container = this.docker.getContainer(containerInfo.containerId);

      // 使用 base64 编码来避免特殊字符问题
      const base64Content = Buffer.from(content).toString('base64');
      const writeCommand = `echo '${base64Content}' | base64 -d > ${filePath}`;

      const writeExec = await container.exec({
        Cmd: ['sh', '-c', writeCommand],
        AttachStdout: true,
        AttachStderr: true
      });

      const stream = await writeExec.start();

      // 等待命令执行完成
      await new Promise((resolve, reject) => {
        let output = '';
        let error = '';

        stream.on('data', (chunk) => {
          const data = chunk.toString();
          if (chunk[0] === 1) { // stdout
            output += data.slice(8);
          } else if (chunk[0] === 2) { // stderr
            error += data.slice(8);
          }
        });

        stream.on('end', () => {
          if (error && error.trim()) {
            reject(new Error(error));
          } else {
            resolve();
          }
        });

        stream.on('error', reject);
      });

      console.log(`文件写入成功: ${filePath}`);

      // 自动配置环境
      await this.autoConfigureEnvironment(userId, filePath, content);

      return { success: true, path: filePath };
    } catch (error) {
      console.error('写入文件失败:', error);
      return { success: false, error: error.message };
    }
  }

  async autoConfigureEnvironment(userId, filePath, content) {
    try {
      const fileExtension = filePath.split('.').pop().toLowerCase();
      const containerInfo = await this.containerManager.getUserContainer(userId);
      if (!containerInfo) return;

      const container = this.docker.getContainer(containerInfo.containerId);

      console.log(`🔧 正在为 ${fileExtension} 文件配置环境...`);

      switch (fileExtension) {
        case 'py':
          await this.configurePythonEnvironment(container, content);
          break;
        case 'js':
          await this.configureNodeEnvironment(container, content);
          break;
        case 'java':
          await this.configureJavaEnvironment(container);
          break;
        case 'cpp':
        case 'c':
          await this.configureCppEnvironment(container);
          break;
        case 'sh':
          await this.configureBashEnvironment(container, filePath);
          break;
        default:
          console.log(`ℹ️ ${fileExtension} 文件无需特殊环境配置`);
      }
    } catch (error) {
      console.error('环境配置失败:', error.message);
    }
  }

  async configurePythonEnvironment(container, content) {
    try {
      // 检查是否需要安装特定的Python包
      const imports = this.extractPythonImports(content);
      const packagesToInstall = this.getPythonPackagesToInstall(imports);

      if (packagesToInstall.length > 0) {
        console.log(`📦 检测到需要安装的Python包: ${packagesToInstall.join(', ')}`);

        for (const pkg of packagesToInstall) {
          console.log(`📥 安装 ${pkg}...`);
          const installExec = await container.exec({
            Cmd: ['pip3', 'install', pkg],
            AttachStdout: true,
            AttachStderr: true
          });
          await installExec.start();
        }
        console.log('✅ Python环境配置完成');
      }
    } catch (error) {
      console.error('Python环境配置失败:', error.message);
    }
  }

  async configureNodeEnvironment(container, content) {
    try {
      // 检查是否需要安装npm包
      const requires = this.extractNodeRequires(content);
      const packagesToInstall = this.getNodePackagesToInstall(requires);

      if (packagesToInstall.length > 0) {
        console.log(`📦 检测到需要安装的Node.js包: ${packagesToInstall.join(', ')}`);

        // 初始化package.json如果不存在
        const initExec = await container.exec({
          Cmd: ['sh', '-c', 'cd /home/<USER>'],
          AttachStdout: true,
          AttachStderr: true
        });
        await initExec.start();

        for (const pkg of packagesToInstall) {
          console.log(`📥 安装 ${pkg}...`);
          const installExec = await container.exec({
            Cmd: ['sh', '-c', `cd /home/<USER>
            AttachStdout: true,
            AttachStderr: true
          });
          await installExec.start();
        }
        console.log('✅ Node.js环境配置完成');
      }
    } catch (error) {
      console.error('Node.js环境配置失败:', error.message);
    }
  }

  async configureJavaEnvironment(container) {
    try {
      console.log('☕ Java环境已就绪');
    } catch (error) {
      console.error('Java环境配置失败:', error.message);
    }
  }

  async configureCppEnvironment(container) {
    try {
      console.log('⚙️ C/C++编译环境已就绪');
    } catch (error) {
      console.error('C/C++环境配置失败:', error.message);
    }
  }

  async configureBashEnvironment(container, filePath) {
    try {
      // 给shell脚本添加执行权限
      const chmodExec = await container.exec({
        Cmd: ['chmod', '+x', filePath],
        AttachStdout: true,
        AttachStderr: true
      });
      await chmodExec.start();
      console.log('📜 Bash脚本权限配置完成');
    } catch (error) {
      console.error('Bash环境配置失败:', error.message);
    }
  }

  extractPythonImports(content) {
    const importRegex = /^(?:from\s+(\S+)\s+)?import\s+(\S+)/gm;
    const imports = [];
    let match;

    while ((match = importRegex.exec(content)) !== null) {
      if (match[1]) {
        imports.push(match[1]); // from xxx import
      } else {
        imports.push(match[2]); // import xxx
      }
    }

    return imports;
  }

  getPythonPackagesToInstall(imports) {
    const commonPackages = {
      'requests': 'requests',
      'numpy': 'numpy',
      'pandas': 'pandas',
      'matplotlib': 'matplotlib',
      'scipy': 'scipy',
      'sklearn': 'scikit-learn',
      'cv2': 'opencv-python',
      'PIL': 'Pillow',
      'flask': 'flask',
      'django': 'django',
      'fastapi': 'fastapi',
      'sqlalchemy': 'sqlalchemy'
    };

    return imports
      .filter(imp => commonPackages[imp])
      .map(imp => commonPackages[imp]);
  }

  extractNodeRequires(content) {
    const requireRegex = /require\(['"`]([^'"`]+)['"`]\)/g;
    const imports = [];
    let match;

    while ((match = requireRegex.exec(content)) !== null) {
      imports.push(match[1]);
    }

    return imports;
  }

  getNodePackagesToInstall(requires) {
    const builtinModules = ['fs', 'path', 'http', 'https', 'url', 'crypto', 'os', 'util'];
    return requires.filter(req => !builtinModules.includes(req) && !req.startsWith('./') && !req.startsWith('../'));
  }

  async deleteFile(userId, filePath) {
    try {
      const containerInfo = await this.containerManager.getUserContainer(userId);
      if (!containerInfo) {
        throw new Error('用户容器不存在');
      }

      const container = this.docker.getContainer(containerInfo.containerId);

      // 使用 rm 命令删除文件
      const exec = await container.exec({
        Cmd: ['rm', '-f', filePath],
        AttachStdout: true,
        AttachStderr: true
      });

      await exec.start();

      console.log(`文件删除成功: ${filePath}`);
      return { success: true, path: filePath };
    } catch (error) {
      console.error('删除文件失败:', error);
      return { success: false, error: error.message };
    }
  }

  async createDirectory(userId, dirPath) {
    try {
      const containerInfo = await this.containerManager.getUserContainer(userId);
      if (!containerInfo) {
        throw new Error('用户容器不存在');
      }

      const container = this.docker.getContainer(containerInfo.containerId);

      // 使用 mkdir 命令创建目录
      const exec = await container.exec({
        Cmd: ['mkdir', '-p', dirPath],
        AttachStdout: true,
        AttachStderr: true
      });

      await exec.start();

      console.log(`目录创建成功: ${dirPath}`);
      return { success: true, path: dirPath };
    } catch (error) {
      console.error('创建目录失败:', error);
      return { success: false, error: error.message };
    }
  }

  async copyFile(userId, sourcePath, destPath) {
    try {
      const containerInfo = await this.containerManager.getUserContainer(userId);
      if (!containerInfo) {
        throw new Error('用户容器不存在');
      }

      const container = this.docker.getContainer(containerInfo.containerId);

      // 使用 cp 命令复制文件
      const exec = await container.exec({
        Cmd: ['cp', sourcePath, destPath],
        AttachStdout: true,
        AttachStderr: true
      });

      await exec.start();

      console.log(`文件复制成功: ${sourcePath} -> ${destPath}`);
      return { success: true, source: sourcePath, destination: destPath };
    } catch (error) {
      console.error('复制文件失败:', error);
      return { success: false, error: error.message };
    }
  }

  async moveFile(userId, sourcePath, destPath) {
    try {
      const containerInfo = await this.containerManager.getUserContainer(userId);
      if (!containerInfo) {
        throw new Error('用户容器不存在');
      }

      const container = this.docker.getContainer(containerInfo.containerId);

      // 使用 mv 命令移动文件
      const exec = await container.exec({
        Cmd: ['mv', sourcePath, destPath],
        AttachStdout: true,
        AttachStderr: true
      });

      await exec.start();

      console.log(`文件移动成功: ${sourcePath} -> ${destPath}`);
      return { success: true, source: sourcePath, destination: destPath };
    } catch (error) {
      console.error('移动文件失败:', error);
      return { success: false, error: error.message };
    }
  }

  async searchFiles(userId, pattern, searchPath = '/home/<USER>') {
    try {
      const containerInfo = await this.containerManager.getUserContainer(userId);
      if (!containerInfo) {
        throw new Error('用户容器不存在');
      }

      const container = this.docker.getContainer(containerInfo.containerId);

      // 使用 find 命令搜索文件
      const exec = await container.exec({
        Cmd: ['find', searchPath, '-name', pattern],
        AttachStdout: true,
        AttachStderr: true
      });

      const stream = await exec.start();

      return new Promise((resolve, reject) => {
        let output = '';
        stream.on('data', (chunk) => {
          output += chunk.toString();
        });

        stream.on('end', () => {
          const files = output.split('\n').filter(line => line.trim());
          resolve({ success: true, files, pattern, searchPath });
        });

        stream.on('error', reject);
      });
    } catch (error) {
      console.error('搜索文件失败:', error);
      return { success: false, error: error.message };
    }
  }

  async getFileInfo(userId, filePath) {
    try {
      const containerInfo = await this.containerManager.getUserContainer(userId);
      if (!containerInfo) {
        throw new Error('用户容器不存在');
      }

      const container = this.docker.getContainer(containerInfo.containerId);

      // 使用 stat 命令获取文件信息
      const exec = await container.exec({
        Cmd: ['stat', filePath],
        AttachStdout: true,
        AttachStderr: true
      });

      const stream = await exec.start();

      return new Promise((resolve, reject) => {
        let output = '';
        stream.on('data', (chunk) => {
          output += chunk.toString();
        });

        stream.on('end', () => {
          resolve({ success: true, info: output, path: filePath });
        });

        stream.on('error', reject);
      });
    } catch (error) {
      console.error('获取文件信息失败:', error);
      return { success: false, error: error.message };
    }
  }
}

export default FileManager;