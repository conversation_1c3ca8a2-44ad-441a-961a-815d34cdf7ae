# CodeSpace Manager

基于 OpenVSCode Server 的智能容器管理系统

## 功能特性

### 🚀 新增功能
- **Docker环境配置**: 在代码执行菜单中新增Docker环境配置选项
- **OpenVSCode Server启动**: 一键启动OpenVSCode Server容器
- **自定义Docker命令**: 支持执行自定义Docker命令
- **Docker状态监控**: 查看Docker服务状态和容器信息

### 📋 主要功能
- 用户管理 (注册/登录)
- 容器管理 (自动创建/启动用户容器)
- AI助手 (代码生成/智能对话)
- 文件管理 (读写/目录操作)
- 代码执行 (多语言支持)
- 包管理 (pip/npm/apt)

## 使用说明

### 启动应用
```bash
cd codespace-manager
node src/index.js
```

### Docker环境配置

1. 在主菜单选择 "⚡ 代码执行"
2. 选择 "🐳 Docker环境配置"
3. 可选择以下操作：
   - **🚀 启动OpenVSCode Server**: 启动预配置的VSCode服务器
   - **⚙️ 自定义Docker命令**: 执行自定义Docker命令
   - **📋 查看Docker状态**: 检查Docker服务状态

### OpenVSCode Server配置

启动OpenVSCode Server时，系统会提示配置：
- **端口号**: 默认7030，可自定义
- **工作目录路径**: 默认/home/<USER>

修复后的Docker命令格式：
```bash
docker run -it --init -p 7030:3000 -v "/home/<USER>/home/<USER>" gitpod/openvscode-server
```

### 错误处理

如果启动失败，系统会提供以下检查建议：
1. Docker是否已安装并运行
2. 端口是否被占用
3. 路径是否正确

## 系统要求

- Node.js 14+
- Docker (用于容器功能)
- 支持的操作系统: Windows/Linux/macOS

## 支持的编程语言

- Python (.py)
- JavaScript (.js)
- Java (.java)
- C++ (.cpp/.cc)
- C (.c)
- Bash (.sh)

## 更新日志

### v1.1.0
- ✅ 清理了多余的测试文件和md文件
- ✅ 新增Docker环境配置菜单
- ✅ 修复OpenVSCode Server启动命令
- ✅ 添加Docker状态监控功能
- ✅ 改进错误处理和用户提示
