<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{8921BEE9-8683-53E5-A950-996D89FAF2AC}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>cpu_features</RootNamespace>
    <IgnoreWarnCompileDuplicatedFilename>true</IgnoreWarnCompileDuplicatedFilename>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props"/>
  <PropertyGroup Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Label="Locals">
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.props"/>
  <ImportGroup Label="ExtensionSettings"/>
  <ImportGroup Label="PropertySheets">
    <Import Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props"/>
  </ImportGroup>
  <PropertyGroup Label="UserMacros"/>
  <PropertyGroup>
    <ExecutablePath>$(ExecutablePath);$(MSBuildProjectDirectory)\..\..\..\deps\cpu_features\bin\;$(MSBuildProjectDirectory)\..\..\..\deps\cpu_features\bin\</ExecutablePath>
    <IntDir>$(Configuration)\obj\$(ProjectName)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <TargetName>$(ProjectName)</TargetName>
    <TargetPath>$(OutDir)\$(ProjectName)$(TargetExt)</TargetPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\v8\include;..\..\..\deps\cpu_features\include;..\..\..\deps\cpu_features\include\internal;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:__cplusplus -std:c++20 /Zm2000 %(AdditionalOptions)</AdditionalOptions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>false</ExceptionHandling>
      <MinimalRebuild>false</MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>false</OmitFramePointers>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=cpu_features;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;_FILE_OFFSET_BITS=64;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;NOMINMAX;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;NDEBUG;STACK_LINE_READER_BUFFER_SIZE=1024;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
      <OutputFile>$(OutDir)$(ProjectName)$(TargetExt)</OutputFile>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\AppData\\Local\\node-gyp\\Cache\\22.17.0\\x64\\node.lib&quot;</AdditionalDependencies>
      <AdditionalOptions>/LTCG:INCREMENTAL /ignore:4199 %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\v8\include;..\..\..\deps\cpu_features\include;..\..\..\deps\cpu_features\include\internal;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=cpu_features;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;_FILE_OFFSET_BITS=64;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;NOMINMAX;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;NDEBUG;STACK_LINE_READER_BUFFER_SIZE=1024;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\v8\include;..\..\..\deps\cpu_features\include;..\..\..\deps\cpu_features\include\internal;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:__cplusplus -std:c++20 /Zm2000 %(AdditionalOptions)</AdditionalOptions>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>false</ExceptionHandling>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>true</OmitFramePointers>
      <Optimization>Full</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=cpu_features;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;_FILE_OFFSET_BITS=64;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;NOMINMAX;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;NDEBUG;STACK_LINE_READER_BUFFER_SIZE=1024;HOST_BINARY=&quot;node.exe&quot;;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
      <OutputFile>$(OutDir)$(ProjectName)$(TargetExt)</OutputFile>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\AppData\\Local\\node-gyp\\Cache\\22.17.0\\x64\\node.lib&quot;</AdditionalDependencies>
      <AdditionalOptions>/LTCG:INCREMENTAL /ignore:4199 %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.17.0\deps\v8\include;..\..\..\deps\cpu_features\include;..\..\..\deps\cpu_features\include\internal;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=cpu_features;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;_FILE_OFFSET_BITS=64;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;NOMINMAX;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;NDEBUG;STACK_LINE_READER_BUFFER_SIZE=1024;HOST_BINARY=&quot;node.exe&quot;;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="..\..\..\deps\cpu_features\cpu_features.gyp"/>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\deps\cpu_features\include\cpu_features_cache_info.h"/>
    <ClInclude Include="..\..\..\deps\cpu_features\include\cpu_features_macros.h"/>
    <ClInclude Include="..\..\..\deps\cpu_features\include\internal\bit_utils.h"/>
    <ClInclude Include="..\..\..\deps\cpu_features\include\internal\filesystem.h"/>
    <ClInclude Include="..\..\..\deps\cpu_features\include\internal\stack_line_reader.h"/>
    <ClInclude Include="..\..\..\deps\cpu_features\include\internal\string_view.h"/>
    <ClInclude Include="..\..\..\deps\cpu_features\include\internal\cpuid_x86.h"/>
    <ClInclude Include="..\..\..\deps\cpu_features\include\cpuinfo_x86.h"/>
    <ClInclude Include="..\..\..\deps\cpu_features\include\internal\windows_utils.h"/>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\deps\cpu_features\src\impl_aarch64_linux_or_android.c">
      <ObjectFileName>$(IntDir)\deps\cpu_features\src\impl_aarch64_linux_or_android.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\deps\cpu_features\src\impl_aarch64_macos_or_iphone.c">
      <ObjectFileName>$(IntDir)\deps\cpu_features\src\impl_aarch64_macos_or_iphone.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\deps\cpu_features\src\impl_aarch64_windows.c">
      <ObjectFileName>$(IntDir)\deps\cpu_features\src\impl_aarch64_windows.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\deps\cpu_features\src\impl_arm_linux_or_android.c">
      <ObjectFileName>$(IntDir)\deps\cpu_features\src\impl_arm_linux_or_android.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\deps\cpu_features\src\impl_mips_linux_or_android.c">
      <ObjectFileName>$(IntDir)\deps\cpu_features\src\impl_mips_linux_or_android.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\deps\cpu_features\src\impl_ppc_linux.c">
      <ObjectFileName>$(IntDir)\deps\cpu_features\src\impl_ppc_linux.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\deps\cpu_features\src\impl_x86_freebsd.c">
      <ObjectFileName>$(IntDir)\deps\cpu_features\src\impl_x86_freebsd.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\deps\cpu_features\src\impl_x86_linux_or_android.c">
      <ObjectFileName>$(IntDir)\deps\cpu_features\src\impl_x86_linux_or_android.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\deps\cpu_features\src\impl_x86_macos.c">
      <ObjectFileName>$(IntDir)\deps\cpu_features\src\impl_x86_macos.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\deps\cpu_features\src\impl_x86_windows.c">
      <ObjectFileName>$(IntDir)\deps\cpu_features\src\impl_x86_windows.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\deps\cpu_features\src\filesystem.c">
      <ObjectFileName>$(IntDir)\deps\cpu_features\src\filesystem.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\deps\cpu_features\src\stack_line_reader.c">
      <ObjectFileName>$(IntDir)\deps\cpu_features\src\stack_line_reader.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\deps\cpu_features\src\string_view.c">
      <ObjectFileName>$(IntDir)\deps\cpu_features\src\string_view.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="D:\Program Files\nodejs\node_modules\npm\node_modules\node-gyp\src\win_delay_load_hook.cc"/>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.targets"/>
  <ImportGroup Label="ExtensionTargets"/>
</Project>
